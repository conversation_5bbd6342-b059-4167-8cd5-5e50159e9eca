{% extends "base.html" %}

{% block title %}About - Simple Flask App{% endblock %}

{% block content %}
<h2>About This Application</h2>

<p>This is a simple Flask web application created to demonstrate basic web development concepts using Python and Flask.</p>

<h3>Features Included:</h3>
<ul>
    <li><strong>Multiple Routes:</strong> Home, About, Hello, and API endpoints</li>
    <li><strong>Template Inheritance:</strong> Using Jinja2 templates with base template</li>
    <li><strong>Dynamic Content:</strong> Server time display and personalized greetings</li>
    <li><strong>JSON API:</strong> RESTful endpoint returning JSON data</li>
    <li><strong>Error Handling:</strong> Custom 404 and 500 error pages</li>
    <li><strong>Responsive Design:</strong> Basic CSS styling for better appearance</li>
</ul>

<h3>Technology Stack:</h3>
<ul>
    <li><strong>Backend:</strong> Python 3.x with Flask framework</li>
    <li><strong>Frontend:</strong> HTML5, CSS3, and Jinja2 templating</li>
    <li><strong>Development:</strong> Flask development server</li>
</ul>

<div style="background-color: #fff3cd; padding: 15px; border-radius: 5px; margin: 20px 0;">
    <h4>Development Notes:</h4>
    <p>This application is configured to run in debug mode, which means:</p>
    <ul>
        <li>Automatic reloading when code changes</li>
        <li>Detailed error messages in the browser</li>
        <li>Interactive debugger for troubleshooting</li>
    </ul>
    <p><strong>Note:</strong> Debug mode should be disabled in production!</p>
</div>

<p><a href="{{ url_for('home') }}">&larr; Back to Home</a></p>
{% endblock %}
