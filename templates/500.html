{% extends "base.html" %}

{% block title %}Server Error - Simple Flask App{% endblock %}

{% block content %}
<h2>500 - Internal Server Error ⚠️</h2>

<p>Sorry, something went wrong on our end. The server encountered an internal error and was unable to complete your request.</p>

<div style="background-color: #f8d7da; padding: 15px; border-radius: 5px; margin: 20px 0;">
    <h3>What happened?</h3>
    <p>An internal server error occurred. This could be due to:</p>
    <ul>
        <li>A programming error in the application</li>
        <li>A temporary server issue</li>
        <li>A database connection problem</li>
        <li>Missing dependencies or configuration</li>
    </ul>
</div>

<h3>What can you do?</h3>
<ul>
    <li>Try refreshing the page</li>
    <li><a href="{{ url_for('home') }}">Go back to the home page</a></li>
    <li>Wait a few minutes and try again</li>
    <li>Contact the administrator if the problem persists</li>
</ul>

<p style="margin-top: 30px;">
    <a href="{{ url_for('home') }}" style="background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
        Take me home
    </a>
</p>
{% endblock %}
