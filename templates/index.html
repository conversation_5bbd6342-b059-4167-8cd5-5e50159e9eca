{% extends "base.html" %}

{% block title %}Home - Simple Flask App{% endblock %}

{% block content %}
<h2>Welcome to the Simple Flask Application!</h2>

<p>This is a basic Flask web application demonstrating:</p>
<ul>
    <li>Routing with Flask</li>
    <li>Template rendering with Jinja2</li>
    <li>Static content serving</li>
    <li>JSON API endpoints</li>
    <li>Error handling</li>
</ul>

<div style="background-color: #e7f3ff; padding: 15px; border-radius: 5px; margin: 20px 0;">
    <h3>Current Server Time</h3>
    <p><strong>{{ current_time }}</strong></p>
</div>

<h3>Try these features:</h3>
<ul>
    <li><a href="{{ url_for('about') }}">Visit the About page</a></li>
    <li><a href="{{ url_for('hello_name', name='World') }}">Say hello to World</a></li>
    <li><a href="{{ url_for('api_status') }}">Check API status (JSON response)</a></li>
</ul>

<div style="background-color: #f0f8ff; padding: 15px; border-radius: 5px; margin: 20px 0;">
    <h4>Quick Start:</h4>
    <p>To run this application:</p>
    <ol>
        <li>Install Flask: <code>pip install flask</code></li>
        <li>Run the app: <code>python simple_flask_application.py</code></li>
        <li>Open your browser to <code>http://localhost:5000</code></li>
    </ol>
</div>
{% endblock %}
