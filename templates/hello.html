{% extends "base.html" %}

{% block title %}Hello {{ name }} - Simple Flask App{% endblock %}

{% block content %}
<h2>Hello, {{ name }}! 👋</h2>

<p>Welcome to our simple Flask application! This page demonstrates dynamic routing where the URL parameter is used to personalize the greeting.</p>

<div style="background-color: #d4edda; padding: 15px; border-radius: 5px; margin: 20px 0;">
    <h3>How this works:</h3>
    <p>The URL pattern <code>/hello/&lt;name&gt;</code> captures whatever you put after <code>/hello/</code> and passes it to the template as a variable.</p>
    <p>Try changing the name in the URL to see different greetings!</p>
</div>

<h3>Try these examples:</h3>
<ul>
    <li><a href="{{ url_for('hello_name', name='Alice') }}">Hello Alice</a></li>
    <li><a href="{{ url_for('hello_name', name='<PERSON>') }}">Hello Bob</a></li>
    <li><a href="{{ url_for('hello_name', name='<PERSON>') }}">Hello Charlie</a></li>
    <li><a href="{{ url_for('hello_name', name='Python') }}">Hello Python</a></li>
</ul>

<p><a href="{{ url_for('home') }}">&larr; Back to Home</a></p>
{% endblock %}
